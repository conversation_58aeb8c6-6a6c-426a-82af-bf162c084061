add_rules("mode.debug", "mode.release")

-- Add required packages
add_requires("glfw", "opengl", "imgui[glfw_opengl3]")

-- Add BASS audio libraries
-- Note: These will need to be manually installed or provided as system libraries
-- BASS libraries are typically not available through package managers

target("pianowo")
    set_kind("binary")
    set_languages("c++17")
    add_files("src/*.cpp")

    -- Add font file as binary data using built-in bin2c rule
    add_rules("utils.bin2c", {extensions = {".ttf"}})
    add_files("GothicA1-Regular.ttf")

    -- Add packages
    add_packages("glfw", "opengl", "imgui")

    -- Debug configuration
    if is_mode("debug") then
        add_cxflags("-g", "-O0")  -- Add debug symbols and disable optimization
        add_ldflags("-g")         -- Add debug symbols to linker
        set_symbols("debug")      -- Enable debug symbols
    end

    -- Set runtime library for Windows builds
    if is_plat("windows") then
        if is_mode("release") then
            set_runtimes("MD")  -- Use dynamic runtime for release builds
        else
            set_runtimes("MDd") -- Use dynamic runtime for debug builds
        end
    end

    -- Platform-specific system libraries
    if is_plat("linux") then
        -- Linux system libraries (NixOS compatible)
        add_syslinks("GL", "X11", "Xrandr", "Xinerama", "Xcursor", "pthread", "dl", "asound")

        -- Note: BASS libraries are loaded dynamically at runtime
        -- No need to link them statically
    elseif is_plat("windows") or is_plat("mingw") then
        -- Windows system libraries
        add_syslinks("opengl32", "gdi32", "user32", "kernel32", "shell32", "winmm")

        -- Windows-specific defines
        add_defines("WIN32", "_WIN32", "NOMINMAX")

        -- Windows-specific settings
        add_defines("_CRT_SECURE_NO_WARNINGS")  -- Disable MSVC security warnings

        -- Add winmm library for all Windows builds
        -- MSVC will use #pragma comment in source, MinGW will use this explicit link
        add_links("winmm")

        -- For MSVC, add additional C runtime libraries to resolve linking issues
        if is_plat("windows") then
            add_syslinks("ucrt", "vcruntime")
        end

        -- Try to add compiler-specific flags (will be ignored if not supported)
        add_cxflags("/utf-8", {try = true})      -- MSVC UTF-8 encoding
        add_cxflags("-Wall", "-Wextra", {try = true})  -- GCC/Clang warnings

        -- Note: BASS libraries (.dll files) should be placed in the executable directory
        -- winmm library is linked for MIDI input support
    end

--
-- If you want to known more usage about xmake, please see https://xmake.io
--
-- ## FAQ
--
-- You can enter the project directory firstly before building project.
--
--   $ cd projectdir
--
-- 1. How to build project?
--
--   $ xmake
--
-- 2. How to configure project?
--
--   $ xmake f -p [macosx|linux|iphoneos ..] -a [x86_64|i386|arm64 ..] -m [debug|release]
--
-- 3. Where is the build output directory?
--
--   The default output directory is `./build` and you can configure the output directory.
--
--   $ xmake f -o outputdir
--   $ xmake
--
-- 4. How to run and debug target after building project?
--
--   $ xmake run [targetname]
--   $ xmake run -d [targetname]
--
-- 5. How to install target to the system directory or other output directory?
--
-- ## Windows Build Instructions
--
-- For MSVC (Visual Studio):
--   $ xmake f -p windows
--   $ xmake
--
-- For MinGW:
--   $ xmake f -p mingw
--   $ xmake
--
-- For cross-compilation from Linux to Windows:
--   $ xmake f -p mingw --cross=x86_64-w64-mingw32-
--   $ xmake
--
--   $ xmake install
--   $ xmake install -o installdir
--
-- 6. Add some frequently-used compilation flags in xmake.lua
--
-- @code
--    -- add debug and release modes
--    add_rules("mode.debug", "mode.release")
--
--    -- add macro definition
--    add_defines("NDEBUG", "_GNU_SOURCE=1")
--
--    -- set warning all as error
--    set_warnings("all", "error")
--
--    -- set language: c99, c++11
--    set_languages("c99", "c++11")
--
--    -- set optimization: none, faster, fastest, smallest
--    set_optimize("fastest")
--
--    -- add include search directories
--    add_includedirs("/usr/include", "/usr/local/include")
--
--    -- add link libraries and search directories
--    add_links("tbox")
--    add_linkdirs("/usr/local/lib", "/usr/lib")
--
--    -- add system link libraries
--    add_syslinks("z", "pthread")
--
--    -- add compilation and link flags
--    add_cxflags("-stdnolib", "-fno-strict-aliasing")
--    add_ldflags("-L/usr/local/lib", "-lpthread", {force = true})
--
-- @endcode
--

